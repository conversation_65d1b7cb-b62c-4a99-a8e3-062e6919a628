#!/usr/bin/env python3
"""
医学影像数据集拆分脚本

将原始的医学影像数据从 data/medical/{sequence_type}/ 格式
转换为训练标准格式 data/medical/{split}/{sequence_type}/ 格式

支持的功能：
1. 自动发现序列类型（t1, t2, t3, flair, dwi等）
2. 按比例拆分训练集、验证集、测试集
3. 支持多种拆分策略（随机、按文件名排序等）
4. 数据完整性检查
5. 支持软链接或硬拷贝
"""

import os
import sys
import shutil
import random
import argparse
from pathlib import Path
from collections import defaultdict
import glob

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class MedicalDataSplitter:
    """医学影像数据拆分器"""
    
    def __init__(self, source_dir, target_dir, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15):
        """
        初始化数据拆分器
        
        Args:
            source_dir (str): 源数据目录，格式为 data/medical/{sequence_type}/
            target_dir (str): 目标数据目录，格式为 data/medical/{split}/{sequence_type}/
            train_ratio (float): 训练集比例
            val_ratio (float): 验证集比例  
            test_ratio (float): 测试集比例
        """
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        
        # 验证比例
        if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
            raise ValueError(f"拆分比例之和必须为1.0，当前为: {train_ratio + val_ratio + test_ratio}")
        
        self.sequence_types = []
        self.file_info = defaultdict(list)
        
    def discover_sequences(self):
        """自动发现序列类型"""
        print(f"正在扫描源目录: {self.source_dir}")
        
        if not self.source_dir.exists():
            raise FileNotFoundError(f"源目录不存在: {self.source_dir}")
        
        # 查找所有子目录
        for item in self.source_dir.iterdir():
            if item.is_dir():
                sequence_name = item.name
                # 查找该序列目录下的nii.gz文件
                nii_files = list(item.glob("*.nii.gz")) + list(item.glob("*.nii"))
                
                if nii_files:
                    self.sequence_types.append(sequence_name)
                    self.file_info[sequence_name] = nii_files
                    print(f"发现序列类型: {sequence_name}, 文件数量: {len(nii_files)}")
                else:
                    print(f"警告: 目录 {sequence_name} 中未找到nii.gz或nii文件")
        
        if not self.sequence_types:
            raise ValueError(f"在 {self.source_dir} 中未找到任何包含医学影像文件的序列目录")
        
        print(f"总共发现 {len(self.sequence_types)} 种序列类型: {self.sequence_types}")
        return self.sequence_types
    
    def validate_data(self):
        """验证数据完整性"""
        print("\n验证数据完整性...")
        
        total_files = 0
        min_files = float('inf')
        max_files = 0
        
        for seq_type in self.sequence_types:
            file_count = len(self.file_info[seq_type])
            total_files += file_count
            min_files = min(min_files, file_count)
            max_files = max(max_files, file_count)
            
            print(f"  {seq_type}: {file_count} 个文件")
            
            # 检查文件是否可读
            for file_path in self.file_info[seq_type][:3]:  # 只检查前3个文件
                if not file_path.exists():
                    print(f"    警告: 文件不存在 {file_path}")
                elif file_path.stat().st_size == 0:
                    print(f"    警告: 文件为空 {file_path}")
        
        print(f"\n数据统计:")
        print(f"  总文件数: {total_files}")
        print(f"  最少文件数: {min_files}")
        print(f"  最多文件数: {max_files}")
        
        # 检查是否有足够的数据进行拆分
        if min_files < 3:
            print(f"警告: 某些序列类型文件数量过少 ({min_files} < 3)，可能影响拆分效果")
        
        return total_files > 0
    
    def split_files(self, strategy='random', seed=42):
        """
        拆分文件
        
        Args:
            strategy (str): 拆分策略，'random' 或 'sorted'
            seed (int): 随机种子
        """
        print(f"\n开始拆分数据，策略: {strategy}")
        
        if strategy == 'random':
            random.seed(seed)
        
        split_info = defaultdict(lambda: defaultdict(list))
        
        for seq_type in self.sequence_types:
            files = self.file_info[seq_type].copy()
            
            if strategy == 'random':
                random.shuffle(files)
            elif strategy == 'sorted':
                files.sort(key=lambda x: x.name)
            
            total_files = len(files)
            train_count = int(total_files * self.train_ratio)
            val_count = int(total_files * self.val_ratio)
            test_count = total_files - train_count - val_count
            
            # 分配文件
            split_info['train'][seq_type] = files[:train_count]
            split_info['val'][seq_type] = files[train_count:train_count + val_count]
            split_info['test'][seq_type] = files[train_count + val_count:]
            
            print(f"  {seq_type}: 训练集 {train_count}, 验证集 {val_count}, 测试集 {test_count}")
        
        return split_info
    
    def create_directory_structure(self):
        """创建目标目录结构"""
        print(f"\n创建目标目录结构: {self.target_dir}")
        
        for split in ['train', 'val', 'test']:
            for seq_type in self.sequence_types:
                target_path = self.target_dir / split / seq_type
                target_path.mkdir(parents=True, exist_ok=True)
                print(f"  创建目录: {target_path}")
    
    def copy_files(self, split_info, copy_mode='copy'):
        """
        复制文件到目标目录
        
        Args:
            split_info (dict): 拆分信息
            copy_mode (str): 复制模式，'copy', 'symlink', 'hardlink'
        """
        print(f"\n开始复制文件，模式: {copy_mode}")
        
        total_copied = 0
        
        for split in ['train', 'val', 'test']:
            for seq_type in self.sequence_types:
                source_files = split_info[split][seq_type]
                target_dir = self.target_dir / split / seq_type
                
                for source_file in source_files:
                    target_file = target_dir / source_file.name
                    
                    try:
                        if copy_mode == 'copy':
                            shutil.copy2(source_file, target_file)
                        elif copy_mode == 'symlink':
                            if target_file.exists():
                                target_file.unlink()
                            target_file.symlink_to(source_file.absolute())
                        elif copy_mode == 'hardlink':
                            if target_file.exists():
                                target_file.unlink()
                            target_file.hardlink_to(source_file)
                        
                        total_copied += 1
                        
                    except Exception as e:
                        print(f"    错误: 复制文件失败 {source_file} -> {target_file}: {e}")
                
                print(f"  {split}/{seq_type}: 复制了 {len(source_files)} 个文件")
        
        print(f"\n总共复制了 {total_copied} 个文件")
        return total_copied
    
    def generate_summary(self, split_info):
        """生成拆分摘要"""
        print("\n" + "="*60)
        print("数据拆分摘要")
        print("="*60)
        
        summary_file = self.target_dir / "split_summary.txt"
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("医学影像数据拆分摘要\n")
            f.write("="*40 + "\n\n")
            f.write(f"源目录: {self.source_dir}\n")
            f.write(f"目标目录: {self.target_dir}\n")
            f.write(f"拆分比例: 训练集 {self.train_ratio}, 验证集 {self.val_ratio}, 测试集 {self.test_ratio}\n\n")
            
            for split in ['train', 'val', 'test']:
                total_files = sum(len(split_info[split][seq]) for seq in self.sequence_types)
                f.write(f"{split.upper()}集 (总计 {total_files} 个文件):\n")
                print(f"{split.upper()}集 (总计 {total_files} 个文件):")
                
                for seq_type in self.sequence_types:
                    count = len(split_info[split][seq_type])
                    f.write(f"  {seq_type}: {count} 个文件\n")
                    print(f"  {seq_type}: {count} 个文件")
                
                f.write("\n")
                print()
        
        print(f"拆分摘要已保存到: {summary_file}")
    
    def run(self, strategy='random', copy_mode='copy', seed=42):
        """
        执行完整的数据拆分流程
        
        Args:
            strategy (str): 拆分策略
            copy_mode (str): 复制模式
            seed (int): 随机种子
        """
        print("开始医学影像数据拆分流程")
        print("="*60)
        
        try:
            # 1. 发现序列类型
            self.discover_sequences()
            
            # 2. 验证数据
            if not self.validate_data():
                raise ValueError("数据验证失败")
            
            # 3. 拆分文件
            split_info = self.split_files(strategy=strategy, seed=seed)
            
            # 4. 创建目录结构
            self.create_directory_structure()
            
            # 5. 复制文件
            copied_count = self.copy_files(split_info, copy_mode=copy_mode)
            
            # 6. 生成摘要
            self.generate_summary(split_info)
            
            print("\n✓ 数据拆分完成！")
            return True

        except Exception as e:
            print(f"\n✗ 数据拆分失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='医学影像数据集拆分工具')

    parser.add_argument('--source', '-s', type=str,
                       default='data/medical',
                       help='源数据目录 (默认: data/medical)')

    parser.add_argument('--target', '-t', type=str,
                       default='data/medical',
                       help='目标数据目录 (默认: data/medical)')

    parser.add_argument('--train-ratio', type=float, default=0.7,
                       help='训练集比例 (默认: 0.7)')

    parser.add_argument('--val-ratio', type=float, default=0.15,
                       help='验证集比例 (默认: 0.15)')

    parser.add_argument('--test-ratio', type=float, default=0.15,
                       help='测试集比例 (默认: 0.15)')

    parser.add_argument('--strategy', choices=['random', 'sorted'], default='random',
                       help='拆分策略 (默认: random)')

    parser.add_argument('--copy-mode', choices=['copy', 'symlink', 'hardlink'], default='copy',
                       help='文件复制模式 (默认: copy)')

    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子 (默认: 42)')

    parser.add_argument('--dry-run', action='store_true',
                       help='仅显示拆分计划，不实际执行')

    args = parser.parse_args()

    # 创建拆分器
    splitter = MedicalDataSplitter(
        source_dir=args.source,
        target_dir=args.target,
        train_ratio=args.train_ratio,
        val_ratio=args.val_ratio,
        test_ratio=args.test_ratio
    )

    if args.dry_run:
        print("DRY RUN 模式 - 仅显示拆分计划")
        print("-" * 40)

        try:
            splitter.discover_sequences()
            splitter.validate_data()
            split_info = splitter.split_files(strategy=args.strategy, seed=args.seed)
            splitter.generate_summary(split_info)
            print("\n✓ DRY RUN 完成")
        except Exception as e:
            print(f"\n✗ DRY RUN 失败: {e}")
    else:
        # 执行实际拆分
        success = splitter.run(
            strategy=args.strategy,
            copy_mode=args.copy_mode,
            seed=args.seed
        )

        if success:
            print(f"\n数据已成功拆分到: {args.target}")
            print("现在可以使用以下命令开始训练:")
            print(f"python src/train_medical.py")
        else:
            sys.exit(1)


if __name__ == "__main__":
    main()
